# AI Strategy to prevent countries at war from joining factions

# Make AI extremely unlikely to join factions when at war
ai_strategy = {
	type = alliance
	id = "any"

	value = -500

	# Only apply when the country should decline due to war
	modifier = {
		factor = 1
		should_decline_faction_due_to_war = yes
	}
}

# Make AI avoid inviting countries that are at war to factions
ai_strategy = {
	type = befriend
	id = "any"

	value = -200

	# Apply to countries that are at war (including those already in factions)
	modifier = {
		factor = 1
		FROM = {
			has_war = yes
		}
	}
}

# Heavily reduce diplomatic acceptance for faction invitations when at war
ai_strategy = {
	type = diplo_action_acceptance
	id = "any"

	target = join_faction
	value = -1000

	# Only when the target country should decline due to war
	modifier = {
		factor = 1
		FROM = {
			should_decline_faction_due_to_war = yes
		}
	}
}

# Make AI less likely to send faction invitations to countries at war
ai_strategy = {
	type = diplo_action_desire
	id = "any"

	target = join_faction
	value = -300

	# Only when targeting countries at war (including those already in factions)
	modifier = {
		factor = 1
		FROM = {
			has_war = yes
		}
	}
}
