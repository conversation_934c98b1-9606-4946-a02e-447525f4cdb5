add_namespace = faction_decline

# Event triggered when a country at war declines a faction invitation
country_event = {
	id = faction_decline.1
	title = faction_decline.1.t
	desc = faction_decline.1.d
	picture = GFX_report_event_generic_diplomacy
	
	is_triggered_only = yes
	
	option = {
		name = faction_decline.1.a
		# The inviting country acknowledges the decline
		add_opinion_modifier = {
			target = FROM
			modifier = declined_faction_at_war
		}
	}
}

# Event for the country declining the invitation (for player feedback)
country_event = {
	id = faction_decline.2
	title = faction_decline.2.t
	desc = faction_decline.2.d
	picture = GFX_report_event_generic_diplomacy
	
	trigger = {
		has_country_flag = decline_faction_invitation_at_war
	}
	
	mean_time_to_happen = {
		days = 1
	}
	
	option = {
		name = faction_decline.2.a
		clr_country_flag = decline_faction_invitation_at_war
	}
}
