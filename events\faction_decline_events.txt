add_namespace = faction_decline

# Startup event to ensure all countries at war have the idea
country_event = {
	id = faction_decline.0
	hidden = yes

	trigger = {
		has_war = yes
		NOT = { is_in_faction = yes }
		NOT = { has_idea = at_war_no_faction }
	}

	mean_time_to_happen = {
		days = 1
	}

	immediate = {
		add_timed_idea = {
			idea = at_war_no_faction
			days = 365
		}
		set_country_flag = auto_decline_faction_while_at_war
	}
}

# Event triggered when a country at war declines a faction invitation
country_event = {
	id = faction_decline.1
	title = faction_decline.1.t
	desc = faction_decline.1.d
	picture = GFX_report_event_generic_diplomacy
	
	is_triggered_only = yes
	
	option = {
		name = faction_decline.1.a
		# The inviting country acknowledges the decline
		add_opinion_modifier = {
			target = FROM
			modifier = declined_faction_at_war
		}
	}
}

# Event for the country declining the invitation (for player feedback)
country_event = {
	id = faction_decline.2
	title = faction_decline.2.t
	desc = faction_decline.2.d
	picture = GFX_report_event_generic_diplomacy
	
	trigger = {
		has_country_flag = decline_faction_invitation_at_war
	}
	
	mean_time_to_happen = {
		days = 1
	}
	
	option = {
		name = faction_decline.2.a
		clr_country_flag = decline_faction_invitation_at_war
	}
}

# Cleanup event to remove the idea when no longer at war
country_event = {
	id = faction_decline.3
	hidden = yes

	trigger = {
		NOT = { has_war = yes }
		has_idea = at_war_no_faction
	}

	mean_time_to_happen = {
		days = 1
	}

	immediate = {
		remove_ideas = at_war_no_faction
		clr_country_flag = auto_decline_faction_while_at_war
	}
}
