# Scripted triggers for faction-war interactions

# Check if a country should decline faction invitations due to being at war
should_decline_faction_due_to_war = {
	has_war = yes
	NOT = { is_in_faction = yes }
}

# Check if a country is eligible to be invited to a faction (not at war)
eligible_for_faction_invitation = {
	NOT = { has_war = yes }
	NOT = { is_in_faction = yes }
	is_subject = no
}

# Check if a country can send faction invitations (should avoid inviting countries at war)
can_send_faction_invitation_to = {
	FROM = {
		eligible_for_faction_invitation = yes
	}
}
