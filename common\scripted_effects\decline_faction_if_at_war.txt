# Scripted effect to automatically decline faction invitations when at war

decline_faction_invitation_if_at_war = {
	# Check if the country is at war
	if = {
		limit = {
			has_war = yes
		}
		# Decline the faction invitation
		# This effect should be called in diplomatic events
		set_country_flag = decline_faction_invitation_at_war
		
		# Add a temporary modifier to show why the invitation was declined
		add_timed_idea = {
			idea = at_war_no_faction
			days = 30
		}
	}
}

# Effect to check and auto-decline faction invitations
auto_decline_faction_if_at_war = {
	every_country = {
		limit = {
			has_war = yes
			NOT = { is_in_faction = yes }
		}
		# Set flag to decline any incoming faction invitations
		set_country_flag = auto_decline_faction_while_at_war
	}
}
