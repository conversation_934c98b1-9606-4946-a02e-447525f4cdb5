# Scripted effect to automatically decline faction invitations when at war

decline_faction_invitation_if_at_war = {
	# Check if the country is at war
	if = {
		limit = {
			has_war = yes
		}
		# Decline the faction invitation
		# This effect should be called in diplomatic events
		set_country_flag = decline_faction_invitation_at_war

		# Add the permanent idea if not already present
		if = {
			limit = {
				NOT = { has_idea = at_war_no_faction }
			}
			add_ideas = at_war_no_faction
		}
	}
}

# Effect to check and auto-decline faction invitations
auto_decline_faction_if_at_war = {
	# Add idea to ALL countries at war that don't have it (including those in factions)
	every_country = {
		limit = {
			has_war = yes
			NOT = { has_idea = at_war_no_faction }
		}
		# Set flag to decline any incoming faction invitations
		set_country_flag = auto_decline_faction_while_at_war
		# Add the permanent idea
		add_ideas = at_war_no_faction
	}

	# Remove idea from countries no longer at war
	every_country = {
		limit = {
			NOT = { has_war = yes }
			has_idea = at_war_no_faction
		}
		remove_ideas = at_war_no_faction
		clr_country_flag = auto_decline_faction_while_at_war
	}
}
