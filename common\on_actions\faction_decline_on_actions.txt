on_actions = {
	# Triggered when a country receives a faction invitation
	on_faction_invitation = {
		effect = {
			# Check if the invited country is at war
			if = {
				limit = {
					has_war = yes
				}
				# Automatically decline the invitation
				decline_faction_invitation_if_at_war = yes
				
				# Send a message to the inviting country
				FROM = {
					country_event = {
						id = faction_decline.1
						days = 1
					}
				}
			}
		}
	}
	
	# Daily check to set flags for countries at war
	on_daily = {
		effect = {
			auto_decline_faction_if_at_war = yes
		}
	}
	
	# When war starts, set the decline flag and add the idea
	on_war_relation_added = {
		effect = {
			if = {
				limit = {
					NOT = { is_in_faction = yes }
				}
				set_country_flag = auto_decline_faction_while_at_war
				add_timed_idea = {
					idea = at_war_no_faction
					days = 365
				}
			}
		}
	}
	
	# When war ends, remove the decline flag and idea
	on_war_relation_removed = {
		effect = {
			if = {
				limit = {
					NOT = { has_war = yes }
					has_country_flag = auto_decline_faction_while_at_war
				}
				clr_country_flag = auto_decline_faction_while_at_war
				remove_ideas = at_war_no_faction
			}
		}
	}
}
